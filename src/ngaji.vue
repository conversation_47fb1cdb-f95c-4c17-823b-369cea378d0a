<template>
  <div class="page-wrapper">
    <div class="content-area">
      <div class="form-container" :class="{ hidden: showSuccess }">
        <div class="event-title" v-html="displayAcara || 'UMUM'"></div>
        <div class="form-title">ABSENSI</div>
        <div class="form-date">{{ formatDate(formData.tanggal) }}</div>
        <form @submit.prevent="submitForm">
          <input
            type="text"
            v-model="formData.nama"
            placeholder="NAMA"
            required
          />
          <div class="suggestions-container">
            <input
              type="text"
              v-model="kelompokInput"
              @input="handleKelompokInput"
              @keyup="handleKelompokKeyup"
              @compositionend="handleCompositionEnd"
              @focus="handleKelompokFocus"
              @blur="handleKelompokBlur"
              ref="kelompokInputEl"
              :placeholder="placeholderText"
              required
            />
            <div class="suggestions-wrapper">
              <div
                class="suggestions"
                v-if="
                  showSuggestions && kelompokInput && filteredKelompok.length
                "
              >
                <div
                  v-for="item in filteredKelompok"
                  :key="`${item.kelompok}-${item.desa}`"
                  class="suggestion-item"
                  @click.stop="selectKelompok(item)"
                >
                  {{ item.kelompok }} ({{ item.desa }})
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" v-model="formData.detail_ranah" />
          <input type="hidden" v-model="formData.ranah" />
          <input type="hidden" v-model="formData.jam_hadir" />
          <input type="hidden" v-model="formData.lokasi" />
          <input type="hidden" v-model="formData.tanggal" />
          <button type="submit">Kirim Data</button>
        </form>
      </div>

      <!-- Success message -->
      <div v-if="showSuccess" class="form-container">
        <div class="confirmation-message">
          DATA ABSEN ANDA<br />SUDAH KAMI TERIMA.<br /><br />Alhamdulillah<br />Jazaa
          Kumullohu Khoiro.
        </div>
        <button @click="resetForm">Kembali</button>
      </div>
    </div>

    <!-- Warning message at the bottom -->
    <div class="footer-area">
      <div class="warning-container">
        WARNING!!!<br />DILARANG mengoperasikan HP<br />selama acara
        berlangsung.
      </div>
    </div>
  </div>
</template>

<script>
// Add flatMap polyfill for older browsers
if (!Array.prototype.flatMap) {
  Array.prototype.flatMap = function (callback) {
    return Array.prototype.concat.apply([], this.map(callback));
  };
}

/**
 * Purges application cache
 * @param {Object} options - Cache purging options
 * @param {boolean} options.localStorage - Whether to clear localStorage cache
 * @param {boolean} options.sessionStorage - Whether to clear sessionStorage cache
 * @param {boolean} options.memoryCache - Whether to clear in-memory cache
 * @param {Array<string>} options.specificKeys - Specific cache keys to purge
 * @returns {Object} Result of the cache purging operation
 */
function purgeCache(options = {}) {
  const result = { success: true, purged: [] };

  try {
    // Clear localStorage cache
    if (options.localStorage) {
      localStorage.clear();
      result.purged.push("localStorage");
    }

    // Clear sessionStorage cache
    if (options.sessionStorage) {
      sessionStorage.clear();
      result.purged.push("sessionStorage");
    }

    // Clear specific keys if provided
    if (options.specificKeys && Array.isArray(options.specificKeys)) {
      for (const key of options.specificKeys) {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
        result.purged.push(`key:${key}`);
      }
    }

    // For in-memory cache, force a hard reload if needed
    if (options.memoryCache) {
      result.purged.push("memoryCache");
      // Optionally force reload: window.location.reload(true);
    }

    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
}

export default {
  data() {
    return {
      formData: {
        nama: "",
        ranah: "",
        detail_ranah: "",
        jam_hadir: "",
        tanggal: "",
        acara: "", // Raw value from URL
        lokasi: "", // Will be populated from URL
      },
      showSuccess: false,
      kelompokInput: "",
      previousKelompokInput: "", // Track previous input to detect deletions
      showSuggestions: false, // Changed to false by default
      kelompokOptions: {}, // Initialize empty, will be populated from API
      isLoading: true, // Add loading state
      loadError: null, // Track loading errors
      dataLoaded: false, // Track if API data is loaded
      placeholderText: "---", // Default placeholder
      displayAcara: "", // For display purposes
      isMobileKeyboardVisible: false, // New state for mobile keyboard tracking
      inputTimer: null,
      isComposing: false,
    };
  },
  computed: {
    flattenedKelompok() {
      return Object.entries(this.kelompokOptions).flatMap(([desa, kelompoks]) =>
        kelompoks.map((kelompok) => ({ desa, kelompok })),
      );
    },
    filteredKelompok() {
      const searchTerm = this.kelompokInput.toLowerCase();
      console.log(
        `Computing filteredKelompok with search term: "${searchTerm}"`,
      );

      if (!searchTerm || searchTerm.length < 1) {
        console.log("Search term too short or empty, returning empty results");
        return [];
      }

      if (!this.dataLoaded) {
        console.log("Data not yet loaded, returning empty array");
        return [];
      }

      console.log(
        `Finding matches in ${this.flattenedKelompok.length} total options`,
      );
      const filtered = this.flattenedKelompok.filter(
        (item) =>
          item.kelompok.toLowerCase().includes(searchTerm) ||
          item.desa.toLowerCase().includes(searchTerm),
      );
      console.log(
        `Found ${filtered.length} initial matches for "${searchTerm}"`,
      );

      const unique = [];
      const seen = new Set();
      for (const item of filtered) {
        const identifier = `${item.kelompok.toLowerCase()}-${item.desa.toLowerCase()}`;
        if (!seen.has(identifier)) {
          seen.add(identifier);
          unique.push(item);
        }
      }
      console.log(`Returned ${unique.length} unique matches for suggestions`);
      return unique;
    },
  },
  watch: {
    kelompokInput(newVal) {
      console.log(
        `kelompokInput changed to "${newVal}" (length: ${newVal.length})`,
      );
      const shouldShow = newVal.length >= 1;
      console.log(
        `Setting showSuggestions to ${shouldShow} based on input length`,
      );
      this.showSuggestions = shouldShow;
    },
  },
  methods: {
    formatDate(dateString) {
      const days = [
        "Minggu",
        "Senin",
        "Selasa",
        "Rabu",
        "Kamis",
        "Jumat",
        "Sabtu",
      ];
      const months = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember",
      ];
      const date = new Date(`${dateString}T00:00:00`);
      const jakartaDate = new Date(
        date.toLocaleString("en-US", { timeZone: "Asia/Jakarta" }),
      );
      return `${days[jakartaDate.getDay()]}, ${jakartaDate.getDate()} ${months[jakartaDate.getMonth()]} ${jakartaDate.getFullYear()}`;
    },
    getUrlParameter(name) {
      return new URLSearchParams(window.location.search).get(name) || "";
    },
    handleKelompokInput(_event) {
      console.log(
        `handleKelompokInput called with current input: "${this.kelompokInput}"`,
      );

      // Clear any pending timers to prevent race conditions
      if (this.inputTimer) {
        clearTimeout(this.inputTimer);
      }

      // Use timer to process input after a short delay
      this.inputTimer = setTimeout(() => {
        // If the input is empty but the element has value, synchronize them
        const inputEl = this.$refs.kelompokInputEl;
        if (!this.kelompokInput && inputEl && inputEl.value) {
          console.log(`Synchronizing input value: "${inputEl.value}"`);
          this.kelompokInput = inputEl.value;
        }

        // Check if user is deleting a character
        if (this.kelompokInput.length < this.previousKelompokInput.length) {
          console.log("Deletion detected, clearing input");
          // User is deleting - clear the entire input
          this.kelompokInput = "";
        }

        // Store current value for next comparison
        this.previousKelompokInput = this.kelompokInput;

        // Only show suggestions when there is at least 1 character
        const shouldShow = this.kelompokInput.length >= 1;
        console.log(
          `Setting showSuggestions to ${shouldShow} based on input length (${this.kelompokInput.length})`,
        );
        this.showSuggestions = shouldShow;

        // Only clear form data if user is typing something new, not after selection
        // Check if input doesn't match the pattern of a selected item "kelompok (desa)"
        if (
          !this.kelompokInput.includes(" (") ||
          !this.kelompokInput.includes(")")
        ) {
          console.log("New input detected, clearing previous selection");
          this.formData.detail_ranah = "";
          this.formData.ranah = "";
        }

        if (!this.dataLoaded && !this.isLoading) {
          console.log("Data not loaded, retrying fetch...");
          this.fetchKelompokData();
        }
      }, 50); // Small delay to ensure we get complete input
    },
    handleKelompokFocus() {
      console.log(
        `handleKelompokFocus called with current input: "${this.kelompokInput}"`,
      );
      // Show suggestions when there is at least 1 character
      const shouldShow = this.kelompokInput.length >= 1;
      console.log(
        `Focus event: Setting showSuggestions to ${shouldShow} (input length: ${this.kelompokInput.length})`,
      );
      this.showSuggestions = shouldShow;
    },
    handleKelompokBlur() {
      console.log("handleKelompokBlur called, scheduling suggestion hide");
      // Only hide suggestions if we're not in the middle of a selection
      if (!this._selectionInProgress) {
        setTimeout(() => {
          console.log("Blur timeout executed, hiding suggestions");
          this.showSuggestions = false;
        }, 150);
      }
    },
    selectKelompok(item) {
      this._selectionInProgress = true;
      console.log(
        `selectKelompok called with item: ${item.kelompok} (${item.desa})`,
      );
      this.kelompokInput = `${item.kelompok} (${item.desa})`;

      // Update form data values immediately - no delay
      this.formData.detail_ranah = item.kelompok;
      this.formData.ranah = item.desa;

      // Force Vue to recognize the changes
      this.formData = { ...this.formData };

      console.log("Form data updated immediately:", {
        detail_ranah: this.formData.detail_ranah,
        ranah: this.formData.ranah,
      });

      // Hide suggestions after selection for better UX
      setTimeout(() => {
        this.showSuggestions = false;
        this._selectionInProgress = false;

        // Double-check that data is still there
        console.log("Verification after timeout:", {
          detail_ranah: this.formData.detail_ranah,
          ranah: this.formData.ranah,
        });
      }, 200);
    },
    validateForm() {
      if (!this.formData.nama.trim()) {
        alert("Nama harus diisi");
        return false;
      }

      if (!this.formData.detail_ranah || !this.formData.ranah) {
        alert("Kelompok harus dipilih dari daftar yang tersedia");
        return false;
      }

      const isValidKelompok = this.flattenedKelompok.some(
        (item) =>
          item.kelompok.toLowerCase() ===
            this.formData.detail_ranah.toLowerCase() &&
          item.desa.toLowerCase() === this.formData.ranah.toLowerCase(),
      );
      if (!isValidKelompok) {
        console.error("Validation failed: Invalid kelompok selection", {
          input: {
            detail_ranah: this.formData.detail_ranah,
            ranah: this.formData.ranah,
          },
          availableOptions: this.flattenedKelompok,
        });
        alert(
          "Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik",
        );
        return false;
      }

      console.log("Form validation successful ✅");
      return true;
    },
    async submitForm() {
      console.group("Form Submission Process");
      console.log("Starting form submission...");
      console.log(
        "Form data before validation:",
        JSON.stringify(this.formData),
      );

      if (!this.validateForm()) {
        console.warn("Form validation failed, submission aborted");
        console.groupEnd();
        return;
      }

      console.log("Generating detailed timestamp using Asia/Jakarta timezone");

      const now = new Date();
      console.log("Original Date object:", now);
      console.log(
        "Browser timezone:",
        Intl.DateTimeFormat().resolvedOptions().timeZone,
      );

      const todayTime = new Date(
        now.toLocaleString("en-US", {
          timeZone: "Asia/Jakarta",
        }),
      );

      console.log("Date conversion details:", {
        originalTimestamp: now.toISOString(),
        utcTime: now.toUTCString(),
        convertedTimestamp: todayTime.toISOString(),
        convertedUTC: todayTime.toUTCString(),
        rawLocaleString: now.toLocaleString("en-US", {
          timeZone: "Asia/Jakarta",
        }),
      });

      this.formData.jam_hadir = `${todayTime.getHours().toString().padStart(2, "0")}:${todayTime.getMinutes().toString().padStart(2, "0")}`;
      this.formData.tanggal = todayTime.toLocaleDateString("en-CA", {
        timeZone: "Asia/Jakarta",
      });

      console.log("Generated timestamp details:", {
        jam_hadir: this.formData.jam_hadir,
        tanggal: this.formData.tanggal,
        hours: todayTime.getHours(),
        minutes: todayTime.getMinutes(),
        day: todayTime.getDate(),
        month: todayTime.getMonth() + 1,
        year: todayTime.getFullYear(),
        rawDate: todayTime.toString(),
      });

      const apiKey = this.getUrlParameter("key");
      console.log("API Key detection:", {
        present: !!apiKey,
        keyLength: apiKey ? apiKey.length : 0,
        firstFourChars: apiKey ? `${apiKey.substring(0, 4)}...` : "none",
      });

      if (!apiKey) {
        console.error("API key not found in URL parameters");
        console.log("Full URL:", window.location.href);
        console.log(
          "URL params:",
          new URLSearchParams(window.location.search).toString(),
        );
        alert(
          "Kunci API tidak ditemukan. Silakan berikan kunci yang valid di URL",
        );
        console.groupEnd();
        return;
      }

      try {
        console.log("Preparing form payload for submission...");
        console.log(
          "Original form data:",
          JSON.stringify(this.formData, null, 2),
        );

        // Debug form input focus history
        console.log(
          "Input field focus history:",
          this._debugInputHistory || "Not tracked",
        );
        console.log(
          "Last focused element:",
          document.activeElement
            ? document.activeElement.id || document.activeElement.tagName
            : "None",
        );

        const payload = {
          ...this.formData,
          nama: this.formData.nama.trim(),
          detail_ranah: this.formData.detail_ranah.trim(),
          ranah: this.formData.ranah.trim(),
        };

        console.log("Processed payload:", JSON.stringify(payload, null, 2));
        console.log("Browser details:", {
          userAgent: navigator.userAgent,
          language: navigator.language,
          cookiesEnabled: navigator.cookieEnabled,
          onLine: navigator.onLine,
          screenSize: `${window.screen.width}x${window.screen.height}`,
          viewport: `${window.innerWidth}x${window.innerHeight}`,
        });

        const formData = new FormData();
        for (const [key, value] of Object.entries(payload)) {
          formData.append(key, value);
        }

        console.log("Sending form data to API endpoint...");

        if (!navigator.onLine) {
          throw new Error(
            "Tidak ada koneksi internet. Mohon periksa koneksi Anda dan coba lagi.",
          );
        }

        const response = await fetch("/api/absen-pengajian/", {
          method: "POST",
          headers: {
            Authorization: `ApiKey ${apiKey}`,
          },
          body: formData,
        });

        let responseData;
        try {
          const responseText = await response.text();
          try {
            responseData = JSON.parse(responseText);
          } catch (e) {
            console.error("Failed to parse response:", responseText);
            throw new Error(
              "Respons server tidak valid. Silakan coba lagi nanti.",
            );
          }
        } catch (e) {
          console.error("Failed to parse response as JSON:", e);
          alert(
            "Terjadi kesalahan saat memproses respons dari server. Silakan coba lagi nanti.",
          );
          return;
        }

        if (response.status === 422) {
          const errorMessage = responseData.detail
            ? Array.isArray(responseData.detail)
              ? responseData.detail.join("\n")
              : responseData.detail
            : "Data yang dikirim tidak valid";
          alert(errorMessage);
          return;
        }

        if (response.ok && responseData.id) {
          this.showSuccess = true;
        } else if (responseData.detail) {
          const detail = Array.isArray(responseData.detail)
            ? responseData.detail[0]
            : responseData.detail;

          if (
            typeof detail === "string" &&
            detail.includes("Duplicate entry detected")
          ) {
            alert("Data yang sama sudah dimasukkan, silakan cek kembali");
          } else {
            console.error("Server error detail:", detail);
            alert(`Error: ${detail}`);
          }
        } else {
          throw new Error(
            "Server tidak merespon dengan benar. Silakan coba beberapa saat lagi.",
          );
        }
      } catch (error) {
        console.error("Network or system error:", error);
        alert(
          `Gagal mengirim data: ${error.message || "Silakan periksa koneksi internet Anda dan coba lagi"}`,
        );
      } finally {
        console.groupEnd();
      }
    },
    resetForm() {
      window.location.reload();
    },
    async fetchKelompokData() {
      console.log("Fetching kelompok data...");
      try {
        const dataParam = this.getUrlParameter("data");
        if (!dataParam) {
          throw new Error("Parameter data tidak ditemukan di URL");
        }

        const encodedParam = encodeURIComponent(dataParam);
        const response = await fetch(`/api/data/daerah/${encodedParam}/`);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const data = await response.json();
        const formattedData = {};

        for (const item of data) {
          if (!formattedData[item.ranah]) {
            formattedData[item.ranah] = [];
          }
          formattedData[item.ranah].push(item.detail_ranah);
        }

        this.kelompokOptions = formattedData;
        this.isLoading = false;
        this.loadError = null;
        this.dataLoaded = true;
      } catch (error) {
        console.error("Error fetching kelompok data:", error);
        this.loadError =
          "Gagal memuat data kelompok. Silakan muat ulang halaman.";
        this.isLoading = false;
        this.dataLoaded = false;

        setTimeout(() => {
          this.fetchKelompokData();
        }, 5000);
      }
    },
    processDisplayText(text) {
      // Handle both double spaces and explicit newlines
      return text.replace(/[ ]{2}|\n/g, "<br>");
    },
    handleKelompokKeyup(_event) {
      // Get input value directly from the DOM element to work around v-model sync issues
      const inputEl = this.$refs.kelompokInputEl;
      if (inputEl && this.kelompokInput !== inputEl.value) {
        console.log(
          `Keyup detected value mismatch. v-model: "${this.kelompokInput}", element: "${inputEl.value}"`,
        );
        this.kelompokInput = inputEl.value;
        this.handleKelompokInput();
      }
    },

    handleCompositionEnd(event) {
      // For IME input handling (like Chinese, Japanese, Korean keyboards)
      this.isComposing = false;
      console.log(`Composition ended with text: "${event.data}"`);
      this.kelompokInput = event.target.value;
      this.handleKelompokInput();
    },
  },
  async mounted() {
    console.log("Component mounted");

    // Handle potential script loading issues
    window.addEventListener(
      "error",
      (event) => {
        if (event.filename?.includes("ngaji.brkh.work")) {
          console.warn("Blocked script loading error:", event.filename);
          event.preventDefault();
        }
      },
      true,
    );

    // Purge application cache on load
    try {
      const purgeResult = purgeCache({
        localStorage: false,
        sessionStorage: true,
        memoryCache: true,
        specificKeys: ["ngajiFormData", "ngajiLastSubmission"],
      });
      console.log("Cache purge result:", purgeResult);
    } catch (err) {
      console.warn("Cache purging failed:", err);
    }
    // Add visibility change detection for mobile keyboard
    const visualViewport = window.visualViewport;
    if (visualViewport) {
      visualViewport.addEventListener("resize", () => {
        const wasKeyboardVisible = this.isMobileKeyboardVisible;
        this.isMobileKeyboardVisible =
          visualViewport.height < window.innerHeight * 0.8;

        console.log(
          `Viewport resize: keyboard visible changed from ${wasKeyboardVisible} to ${this.isMobileKeyboardVisible}`,
        );
        console.log(
          `Viewport height: ${visualViewport.height}, Window height: ${window.innerHeight}`,
        );

        if (this.isMobileKeyboardVisible && this.kelompokInput) {
          console.log(
            "Keyboard detected AND input has value, showing suggestions",
          );
          this.showSuggestions = true;
        } else if (!this.isMobileKeyboardVisible && wasKeyboardVisible) {
          console.log("Keyboard hidden, suggestion visibility unchanged");
        }
      });
    }
    try {
      await fetch(window.location.pathname, {
        cache: "reload",
        credentials: "same-origin",
      });
    } catch (e) {
      console.warn("Cache clearing failed:", e);
    }

    const now = new Date();
    const todayTime = new Date(
      now.toLocaleString("en-US", {
        timeZone: "Asia/Jakarta",
      }),
    );
    this.formData.tanggal = todayTime.toLocaleDateString("en-CA", {
      timeZone: "Asia/Jakarta",
    });

    const rawAcara = this.getUrlParameter("acara");
    this.formData.acara = rawAcara;
    this.displayAcara = this.processDisplayText(rawAcara);

    this.formData.lokasi = this.getUrlParameter("lokasi");
    const customPlaceholder = this.getUrlParameter("ph");
    if (customPlaceholder) {
      this.placeholderText = customPlaceholder;
    }

    document.title = `Absensi Acara - ${rawAcara.replace(/\s{2,}/g, " - ")}`;

    await this.fetchKelompokData();

    // Add event listener to detect device keyboard issues
    document.addEventListener("touchend", (event) => {
      if (event.target === this.$refs.kelompokInputEl) {
        setTimeout(() => {
          const inputEl = this.$refs.kelompokInputEl;
          if (inputEl && this.kelompokInput !== inputEl.value) {
            console.log(
              `Touch event detected value mismatch. v-model: "${this.kelompokInput}", element: "${inputEl.value}"`,
            );
            this.kelompokInput = inputEl.value;
            this.handleKelompokInput();
          }
        }, 100);
      }
    });
  },
};
</script>

<style>
body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue",
    Arial, sans-serif;
  margin: 0;
  padding: 0;
  background-color: #ffffff;
  color: #2c4a3e;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

/* Page structure */
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
  padding-bottom: 0; /* Space for the warning container */
  width: 100%;
}

.content-area {
  flex: 1;
  position: relative;
}

.footer-area {
  position: fixed;
  width: 100%;
  margin-top: auto;
  padding-bottom: 5px;
  bottom: 0;
}

/* Form container positioning */
.form-container {
  width: 100%;
  max-width: 360px;
  padding: 20px;
  border-radius: 20px;
  background-color: #f9f9f9;
  box-shadow:
    0 10px 25px rgba(44, 74, 62, 0.2),
    0 6px 12px rgba(44, 74, 62, 0.15);
  box-sizing: border-box;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease-in-out;
  z-index: 100;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}

.form-title {
  font-size: 20px;
  font-weight: bold;
  color: #2e5a35;
  margin-bottom: 10px;
}

.form-date {
  font-size: 16px;
  color: #2e5a35;
  margin-bottom: 20px;
  opacity: 0.8;
}

input,
select {
  width: 100%;
  padding: 12px;
  margin-bottom: 15px;
  border: 1px solid #2e5a35;
  border-radius: 20px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
  appearance: none;
  background-color: #ffffff;
  color: #2c4a3e;
}

input::placeholder {
  color: #2e5a35;
  opacity: 0.9;
}

input:focus,
select:focus {
  border-color: #2e5a35;
  border-width: 2px;
  outline: none;
}

button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 20px;
  background-color: #2e5a35;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-sizing: border-box;
  margin-top: 10px;
}

button:hover {
  background-color: #3d7a47;
}

.confirmation-message {
  font-size: 20px;
  font-weight: bold;
  color: #2e5a35;
}

.suggestions {
  position: absolute;
  width: 100%;
  max-width: 360px;
  max-height: 30vh;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #2e5a35;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 100000;
  top: 100%;
  margin-top: 5px;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.suggestion-item {
  padding: 10px;
  cursor: pointer;
  text-align: left;
  color: #2c4a3e;
  touch-action: pan-y;
}

.suggestion-item {
  padding: 10px;
  cursor: pointer;
  text-align: left;
  color: #2c4a3e;
}

.suggestion-item:hover {
  background-color: #e8f3ed;
}

.event-title {
  margin: 25px 0 25px 0;
  font-weight: bold;
  color: #2e5a35;
  font-size: 20px;
  line-height: 1.6;
}

.event-title br {
  display: block;
  margin: 8px 0;
  content: "";
}

.suggestions-container {
  touch-action: pan-y;
  position: relative;
  z-index: 200000 !important;
}

.suggestions-wrapper {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 200000 !important;
}

.suggestions {
  width: 100%;
  max-width: 360px;
  max-height: 30vh;
  overflow-y: auto;
  background: #ffffff;
  border: 1px solid #2e5a35;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  margin-top: 5px;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  z-index: 200000 !important;
}

/* Warning container - make it static */
.warning-container {
  width: 100%;
  max-width: 360px;
  padding: 15px;
  margin: 0 auto;
  border-radius: 10px;
  background-color: #cc0000;
  color: #fff;
  box-shadow: 0 4px 8px rgba(44, 74, 62, 0.1);
  box-sizing: border-box;
  text-align: center;
  font-weight: bold;
  animation: blinkColors 1s infinite;
  z-index: 50;
  line-height: 1.25;
}

@keyframes blinkColors {
  0%,
  100% {
    background-color: #cc0000;
    color: #fff;
  }

  50% {
    background-color: #fff;
    color: #cc0000;
  }
}
</style>
