<template>
  <div id="pantau-ngaji-cek">
    <h1>-- VERSI BETA RELEASE --</h1>
    <h1>Pantauan Absen Per Kelompok</h1>
    <div style="margin-bottom: 10px; text-align: center; font-weight: bold">
      {{ filters.acara || "UMUM" }}
    </div>
    <section>
      <div class="filter-item">
        <div>
          <label for="desaFilter">Filter Desa:</label>
          <select v-model="filters.ranah" @change="filterTable" id="desaFilter">
            <option value="">Semua</option>
            <option v-for="ranah in uniqueRanah" :key="ranah" :value="ranah">
              {{ ranah }}
            </option>
          </select>
        </div>
        <div>
          <label for="tanggalFilter">Filter Tanggal:</label>
          <input
            type="date"
            v-model="filters.tanggal"
            @input="handleTanggalChange"
            id="tanggalFilter"
          />
        </div>
        <div>
          <label for="kelompokFilter">Filter Kelompok:</label>
          <input
            type="text"
            v-model="filters.detailRanah"
            @input="filterTable"
            placeholder="Cari kelompok..."
            id="kelompokFilter"
          />
        </div>
      </div>
    </section>
    <div class="table-container" v-if="!isLoading">
      <div class="summary-container">
        <div class="summary-item">
          <span class="summary-label">Total Hadir:</span>
          <span class="summary-value">{{ totalHadir }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Total Belum Hadir:</span>
          <span class="summary-value">{{ totalBelumHadir }}</span>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            <th @click="handleSort('ranah')">
              Desa
              <span v-if="sortKey === 'ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="handleSort('detail_ranah')">
              Kelompok
              <span v-if="sortKey === 'detail_ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="handleSort('hadir')">
              Hadir
              <span v-if="sortKey === 'hadir'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="handleSort('jam_hadir')">
              Jam Hadir
              <span v-if="sortKey === 'jam_hadir'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="row in filteredAndSortedData"
            :key="row.ranah + '-' + row.detail_ranah"
            :class="{
              hadir: row.hadir !== '-',
              'belum-hadir': row.hadir === '-',
            }"
          >
            <td>{{ row.ranah }}</td>
            <td>{{ row.detail_ranah }}</td>
            <td>
              <span v-if="row.hadir !== '-'">{{ row.hadir }}</span>
              <span v-else class="danger-text">-</span>
            </td>
            <td>
              <span v-if="row.hadir !== '-'">{{ row.jam_hadir }}</span>
              <span v-else class="danger-text">belum-hadir</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <div v-else style="text-align: center; padding: 2em">Memuat data...</div>
    <div class="button-container">
      <button @click="refresh">Refresh</button>
      <button @click="downloadPDF">Download PDF</button>
    </div>
  </div>
</template>

<script>
import { jsPDF } from "jspdf";
import "jspdf-autotable";

export default {
  data() {
    return {
      apiKey: "",
      filters: {
        acara: "",
        lokasi: "",
        tanggal: "",
        data: "",
        ranah: "",
        detailRanah: "",
      },
      kelompokList: [],
      attendanceData: [],
      sortKey: "",
      sortOrder: "asc",
      sortCriteria: [], // Array to track multi-level sorting
      isLoading: false,
    };
  },
  computed: {
    uniqueRanah() {
      return [...new Set(this.kelompokList.map((item) => item.ranah))].sort();
    },
    mergedData() {
      // Create a map to group attendees by ranah + detail_ranah
      const attendanceMap = {};

      // Initialize the map with all kelompok
      for (const kelompok of this.kelompokList) {
        const key = `${kelompok.ranah}|${kelompok.detail_ranah}`;
        attendanceMap[key] = {
          ranah: kelompok.ranah,
          detail_ranah: kelompok.detail_ranah,
          hadir: "-",
          jam_hadir: "belum-hadir",
          nama: [],
        };
      }

      // Add attendance data to the map
      for (const item of this.attendanceData) {
        const key = `${item.ranah}|${item.detail_ranah}`;
        if (attendanceMap[key]) {
          if (attendanceMap[key].hadir === "-") {
            attendanceMap[key].hadir = item.nama;
            attendanceMap[key].nama = [item.nama];
          } else {
            attendanceMap[key].nama.push(item.nama);
            attendanceMap[key].hadir = attendanceMap[key].nama.join(", ");
          }
          // Use the first person's attendance time (or can be changed to latest)
          if (attendanceMap[key].jam_hadir === "belum-hadir") {
            attendanceMap[key].jam_hadir = item.jam_hadir;
          }
        }
      }

      // Convert map to array
      return Object.values(attendanceMap);
    },
    filteredAndSortedData() {
      let filtered = this.mergedData;

      if (this.filters.ranah) {
        filtered = filtered.filter((item) => item.ranah === this.filters.ranah);
      }

      if (this.filters.detailRanah) {
        filtered = filtered.filter((item) =>
          item.detail_ranah
            .toLowerCase()
            .includes(this.filters.detailRanah.toLowerCase()),
        );
      }

      if (this.sortCriteria.length === 0) return filtered;

      return [...filtered].sort((a, b) => {
        // First priority: push "belum-hadir" rows to the bottom
        const aNotPresent = a.hadir === "-";
        const bNotPresent = b.hadir === "-";
        if (aNotPresent !== bNotPresent) {
          return aNotPresent ? 1 : -1;
        }

        // Apply multi-column sorting based on sortCriteria
        for (const criterion of this.sortCriteria) {
          const key = criterion.key;
          const order = criterion.order;

          const x = (a[key] || "").toLowerCase();
          const y = (b[key] || "").toLowerCase();

          if (x !== y) {
            return x < y
              ? order === "asc"
                ? -1
                : 1
              : order === "asc"
                ? 1
                : -1;
          }
        }

        return 0;
      });
    },
    totalHadir() {
      return this.filteredAndSortedData.filter((row) => row.hadir !== "-")
        .length;
    },
    totalBelumHadir() {
      return this.filteredAndSortedData.filter((row) => row.hadir === "-")
        .length;
    },
  },
  methods: {
    formatTanggalIndo(dateStr) {
      const date = new Date(dateStr);
      const days = [
        "Minggu",
        "Senin",
        "Selasa",
        "Rabu",
        "Kamis",
        "Jumat",
        "Sabtu",
      ];
      const months = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember",
      ];
      return `${days[date.getDay()]}, ${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
    },
    async refresh() {
      this.isLoading = true;
      try {
        await Promise.all([this.fetchKelompok(), this.fetchAttendance()]);
      } finally {
        this.isLoading = false;
      }
    },
    async fetchKelompok() {
      const url = `/api/data/daerah/${encodeURIComponent(this.filters.data)}`;
      const resp = await fetch(url, {
        headers: { Authorization: `ApiKey ${this.apiKey}` },
      });
      if (!resp.ok) throw new Error("Gagal fetch kelompok");
      const data = await resp.json();
      this.kelompokList = data;
    },
    async fetchAttendance() {
      const query = `/api/absen-pengajian/?tanggal=${this.filters.tanggal}&acara=${encodeURIComponent(this.filters.acara)}&lokasi=${encodeURIComponent(this.filters.lokasi)}`;
      const resp = await fetch(query, {
        headers: { Authorization: `ApiKey ${this.apiKey}` },
      });
      if (!resp.ok) throw new Error("Gagal fetch absen");
      this.attendanceData = await resp.json();
    },
    handleSort(key) {
      // Find if this key is already in sortCriteria
      const existingIndex = this.sortCriteria.findIndex((c) => c.key === key);

      if (existingIndex !== -1) {
        // Toggle order if key exists
        const currentOrder = this.sortCriteria[existingIndex].order;
        this.sortCriteria[existingIndex].order =
          currentOrder === "asc" ? "desc" : "asc";
      } else {
        // Add new criterion
        this.sortCriteria.push({ key, order: "asc" });
      }

      // Update the sortKey and sortOrder for UI arrows
      this.sortKey = key;
      this.sortOrder = this.sortCriteria.find((c) => c.key === key).order;
    },
    filterTable() {
      // Method needed for v-model filters
    },
    downloadPDF() {
      const doc = new jsPDF({ unit: "cm", format: "a4" });
      doc.setFont("times", "normal");
      doc.setFontSize(18);
      doc.text(
        "Daftar Pantauan Absen Kelompok",
        doc.internal.pageSize.getWidth() / 2,
        2,
        { align: "center" },
      );
      doc.setFontSize(12);
      doc.text(
        `Acara: ${this.filters.acara || "UMUM"}`,
        doc.internal.pageSize.getWidth() / 2,
        2.8,
        { align: "center" },
      );
      doc.text(
        `Lokasi: ${this.filters.lokasi || "-"}`,
        doc.internal.pageSize.getWidth() / 2,
        3.3,
        { align: "center" },
      );
      doc.text(
        `Tanggal: ${this.formatTanggalIndo(this.filters.tanggal)}`,
        doc.internal.pageSize.getWidth() / 2,
        3.8,
        { align: "center" },
      );
      doc.autoTable({
        head: [["No", "Desa", "Kelompok", "Hadir", "Jam Hadir"]],
        body: this.filteredAndSortedData.map((row, i) => [
          i + 1,
          row.ranah,
          row.detail_ranah,
          row.hadir,
          row.jam_hadir,
        ]),
        startY: 4.8,
        styles: { fontSize: 10, cellPadding: 0.4 },
        didDrawPage: (_data) => {
          const pageHeight = doc.internal.pageSize.height;
          const pageWidth = doc.internal.pageSize.width;
          doc.setFontSize(9);
          doc.setTextColor(128);
          doc.text(
            `Hal ${doc.internal.getNumberOfPages()}`,
            pageWidth - 1,
            pageHeight - 0.8,
            { align: "right" },
          );
        },
      });
      const name = [
        "PantauAbsen",
        this.filters.lokasi || "ALL",
        this.filters.acara || "UMUM",
        this.filters.tanggal || "",
      ].join("-");
      doc.save(`${name}.pdf`);
    },
    handleTanggalChange() {
      this.refresh();
    },
  },
  watch: {
    "filters.acara"(newValue) {
      document.title = `Pantauan Per Kelompok / Bidang - ${newValue || "UMUM"}`;
    },
  },
  mounted() {
    const params = new URLSearchParams(window.location.search);
    this.apiKey = params.get("key") || "";
    this.filters.acara = params.get("acara") || "";
    this.filters.lokasi = params.get("lokasi") || "";
    this.filters.data = params.get("data") || "";
    const t = new Date();
    this.filters.tanggal = `${t.getFullYear()}-${String(
      t.getMonth() + 1,
    ).padStart(2, "0")}-${String(t.getDate()).padStart(2, "0")}`;
    this.refresh();
    document.title = `Pantauan Per Kelompok / Bidang - ${this.filters.acara || "UMUM"}`;
  },
};
</script>

<style scoped>
/* Include border-box globally within this component */
#pantau-ngaji-cek,
#pantau-ngaji-cek *,
#pantau-ngaji-cek *::before,
#pantau-ngaji-cek *::after {
  box-sizing: border-box;
}

#pantau-ngaji-cek {
  font-family: system-ui, Arial, sans-serif;
  /* allow full width so children align at their breakpoints */
  max-width: 100%;
  margin: 0 auto;
}
h1 {
  text-align: center;
  margin: 25px 0 10px;
  font-size: 2em;
}
.filter-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
  /* include padding in width calculations for proper alignment */
  box-sizing: border-box;
  background-color: #ffffff;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  margin-bottom: 20px;
  /* Let the width be fully fluid by default; specific limits will be
       applied in media-queries so the component adapts better to each
       viewport size  */
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}
.filter-item div {
  display: flex;
  flex-direction: column;
}
.filter-item label {
  margin-bottom: 5px;
  font-weight: bold;
}
select,
input {
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  appearance: none;
  border-radius: 20px;
  box-sizing: border-box;
  width: 100%;
  height: 45px;
}
.table-container {
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 2px 12px #eee;
  margin-top: 10px;
  /* show full table width but allow cell-level scrolling */
  overflow-x: visible;
  /* Fluid width by default so it can shrink with the viewport */
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
}
.summary-container {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background: #f0f6ff;
  border-bottom: 1px solid #e0e0e0;
  border-radius: 12px 12px 0 0;
}
.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.summary-label {
  font-weight: bold;
  font-size: 0.9em;
  color: #555;
}
.summary-value {
  font-size: 1.4em;
  font-weight: bold;
}
table {
  /* fill container width and adjust columns */
  width: 100%;
  border-collapse: collapse;
  /* fixed layout so columns adjust to evenly fill width */
  table-layout: fixed;
}
th,
td {
  /* enable per-cell horizontal scrolling when text is too long */
  border-bottom: 1px solid #eeeeee;
  padding: 10px 16px;
  text-align: left;
  overflow-x: auto;
  white-space: nowrap;
}
th {
  background: #f4f4f8;
  font-weight: bold;
  cursor: pointer;
  white-space: nowrap;
}
th span {
  color: #888;
  font-size: 1.1em;
}
tr.hadir td {
  background: #e8f8ed;
}
tr.belum-hadir td {
  background: #ffeaea;
}
.danger-text {
  color: #c33;
  font-weight: bold;
}
tfoot td {
  background: #f7f7f7;
  border-top: 2px solid #bbb;
}
.button-container {
  text-align: center;
  margin-top: 20px;
  /* keep centered and fluid */
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Tablet and up (≥768px) — set consistent widths */
@media only screen and (min-width: 768px) {
  .filter-item,
  .table-container,
  .button-container {
    box-sizing: border-box;
    max-width: 768px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Phone portrait (≤480px) — ensure full-width usage */
@media only screen and (max-width: 480px) {
  .filter-item,
  .table-container,
  .button-container {
    max-width: 100%;
  }
}
button {
  border: 1px solid #ccc;
  padding: 10px 20px;
  font-size: 16px;
  background-color: #f9f9f9;
  border-radius: 20px;
  cursor: pointer;
  margin: 0 10px;
}
@media (max-width: 660px) {
  .filter-item {
    max-width: 100%;
  }
  th,
  td {
    padding: 7px 4px;
  }
  button {
    width: 100%;
    margin: 5px 0;
  }
}
</style>
