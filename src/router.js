import { createRouter, createWebHistory } from "vue-router";

// Lazy-loaded route components for better performance
const componentMap = {
  Ngaji: () => import("./ngaji.vue"),
  PantauNgaji: () => import("./pantau-ngaji.vue"),
  PantauNgajiCek: () => import("./pantau-ngaji-cek.vue"),
  StatNgaji: () => import("./stat-ngaji.vue"),
  Asrama: () => import("./asrama.vue"),
  PantauAsrama: () => import("./pantau-asrama.vue"),
  StatAsrama: () => import("./stat-asrama.vue"),
  Util: () => import("./util.vue"),
};

const routesConfig = [
  {
    path: "/ngaji",
    name: "<PERSON><PERSON><PERSON>",
    title: "Absensi Acara",
  },
  {
    path: "/pantau-ngaji",
    name: "Pantau<PERSON><PERSON>ji",
    title: "Pantauan Absensi Acara",
  },
  {
    path: "/pantau-ngaji-cek",
    name: "PantauNgajiCek",
    title: "Pantauan Absen Acara",
  },
  {
    path: "/stat-ngaji",
    name: "StatNgaji",
    title: "Statistik Absensi Acara",
  },
  {
    path: "/asrama",
    name: "Asrama",
    title: "Absensi Asramaan",
  },
  {
    path: "/pantau-asrama",
    name: "PantauAsrama",
    title: "Pantauan Absensi Asramaan",
  },
  {
    path: "/stat-asrama",
    name: "StatAsrama",
    title: "Statistik Absensi Asramaan",
  },
  {
    path: "/",
    name: "Util",
    title: "Utilitas",
  },
];

const routes = routesConfig.map((route) => {
  // Destructure and extract needed properties, ignoring title since it's used via route.title
  const { name, path } = route;
  return {
    name,
    path,
    component: componentMap[name],
    meta: {
      title: route.title,
      requiresAuth: false,
    },
  };
});

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
