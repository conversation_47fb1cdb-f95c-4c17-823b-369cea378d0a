<template>
  <div id="app">
    <h1>Pan<PERSON><PERSON></h1>
    <div style="margin-bottom: 15px; font-weight: bold; text-align: center">
      {{ filters.acara || "UMUM" }}
    </div>

    <section>
      <div class="filter-item">
        <div>
          <label for="sesiFilter">Filter Sesi:</label>
          <select v-model="filters.sesi" @change="filterTable" id="sesiFilter">
            <option value="">Semua</option>
            <option v-for="sesi in uniqueSesi" :key="sesi" :value="sesi">
              {{ sesi }}
            </option>
          </select>
        </div>
        <div>
          <label for="desaFilter">Filter Desa:</label>
          <select v-model="filters.ranah" @change="filterTable" id="desaFilter">
            <option value="">Semua</option>
            <option v-for="ranah in uniqueRanah" :key="ranah" :value="ranah">
              {{ ranah }}
            </option>
          </select>
        </div>
        <div>
          <label for="kelompokFilter">Filter Kelompok:</label>
          <select
            v-model="filters.kelompok"
            @change="filterTable"
            id="kelompokFilter"
          >
            <option value="">Semua</option>
            <option
              v-for="kelompok in uniqueKelompok"
              :key="kelompok"
              :value="kelompok"
            >
              {{ kelompok }}
            </option>
          </select>
        </div>
        <div>
          <label for="tanggalFilter">Filter Tanggal:</label>
          <input
            type="date"
            v-model="filters.tanggal"
            @input="fetchDataForDate"
            id="tanggalFilter"
          />
        </div>
        <div>
          <label for="namaFilter">Filter Nama:</label>
          <input
            type="text"
            v-model="filters.nama"
            @input="filterTable"
            placeholder="Cari nama..."
            id="namaFilter"
          />
        </div>
      </div>
    </section>

    <div class="table-container">
      <table id="attendanceTable">
        <thead>
          <tr>
            <th>No.</th>
            <th @click="sortTable('sesi')">
              Sesi
              <span v-if="sortKey === 'sesi'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('nama')">
              Nama
              <span v-if="sortKey === 'nama'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('ranah')">
              Desa
              <span v-if="sortKey === 'ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('detail_ranah')">
              Kelompok
              <span v-if="sortKey === 'detail_ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('jam_hadir')">
              Jam Hadir
              <span v-if="sortKey === 'jam_hadir'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th style="display: none">Tanggal</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in filteredData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.sesi }}</td>
            <td>{{ item.nama }}</td>
            <td>{{ item.ranah }}</td>
            <td>{{ item.detail_ranah }}</td>
            <td>{{ item.jam_hadir }}</td>
            <td style="display: none">{{ item.tanggal }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="button-container">
      <button @click="downloadPDF">Download as PDF</button>
      <button @click="openStatistics">View Statistics</button>
      <button @click="downloadAllPDF">Download All (By Ranah)</button>
    </div>
  </div>
</template>

<script>
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable";

export default {
  name: "PantauAsrama",
  data() {
    return {
      attendanceData: [],
      filters: {
        sesi: "",
        ranah: "",
        kelompok: "",
        nama: "",
        tanggal: "",
        acara: "",
        lokasi: "",
      },
      apiKey: "",
      sortKey: "",
      sortOrder: "asc",
    };
  },
  computed: {
    uniqueSesi() {
      return [...new Set(this.attendanceData.map((item) => item.sesi))].sort();
    },
    uniqueRanah() {
      return [...new Set(this.attendanceData.map((item) => item.ranah))].sort();
    },
    uniqueKelompok() {
      // If a desa (ranah) is selected, only show kelompok from that desa
      const filteredData = this.filters.ranah
        ? this.attendanceData.filter(
            (item) => item.ranah === this.filters.ranah,
          )
        : this.attendanceData;

      return [...new Set(filteredData.map((item) => item.detail_ranah))].sort();
    },
    filteredData() {
      const filtered = this.attendanceData.filter((item) => {
        const sesiMatch = !this.filters.sesi || item.sesi === this.filters.sesi;
        const ranahMatch =
          !this.filters.ranah || item.ranah === this.filters.ranah;
        const kelompokMatch =
          !this.filters.kelompok || item.detail_ranah === this.filters.kelompok;
        const namaMatch =
          !this.filters.nama ||
          item.nama.toLowerCase().includes(this.filters.nama.toLowerCase());
        return sesiMatch && ranahMatch && kelompokMatch && namaMatch;
      });

      if (this.sortKey) {
        filtered.sort((a, b) => {
          let aVal = this.sortKey === "index" ? 1 : a[this.sortKey];
          let bVal = this.sortKey === "index" ? 1 : b[this.sortKey];

          if (typeof aVal === "string") aVal = aVal.toLowerCase();
          if (typeof bVal === "string") bVal = bVal.toLowerCase();

          if (aVal < bVal) return this.sortOrder === "asc" ? -1 : 1;
          if (aVal > bVal) return this.sortOrder === "asc" ? 1 : -1;
          return 0;
        });
      }

      return filtered;
    },
  },
  methods: {
    async fetchDataForDate() {
      const apiUrl = `/api/absen-asramaan/?tanggal=${this.filters.tanggal}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi || ""}`;
      try {
        const response = await fetch(apiUrl, {
          headers: { Authorization: `ApiKey ${this.apiKey}` },
        });
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        this.attendanceData = await response.json();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    },
    formatTanggal(dateStr) {
      const date = new Date(dateStr);
      const hari = [
        "Minggu",
        "Senin",
        "Selasa",
        "Rabu",
        "Kamis",
        "Jumat",
        "Sabtu",
      ];
      const bulan = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember",
      ];
      const dayName = hari[date.getDay()];
      const day = date.getDate();
      const month = bulan[date.getMonth()];
      const year = date.getFullYear();
      return `${dayName}, ${day} ${month} ${year}`;
    },
    async downloadPDF() {
      const doc = new jsPDF({
        unit: "cm",
        format: "a4",
        margins: { top: 1, bottom: 1, left: 1, right: 1 },
      });

      doc.setFont("Times", "normal");
      doc.setFontSize(20);
      doc.text(
        "Laporan Kehadiran Acara",
        doc.internal.pageSize.getWidth() / 2,
        2,
        { align: "center" },
      );

      doc.setFontSize(16);
      doc.text(
        `${this.filters.acara}`,
        doc.internal.pageSize.getWidth() / 2,
        2.8,
        { align: "center" },
      );
      doc.setFontSize(20);
      doc.text(
        `${this.filters.ranah || "Semua"}`,
        doc.internal.pageSize.getWidth() / 2,
        3.6,
        { align: "center" },
      );
      doc.setFontSize(16);
      const formattedTanggal = this.formatTanggal(this.filters.tanggal);
      doc.text(
        `${formattedTanggal}`,
        doc.internal.pageSize.getWidth() / 2,
        4.4,
        { align: "center" },
      );

      autoTable(doc, {
        head: [["No.", "Sesi", "Nama", "Kelompok", "Jam Hadir"]],
        body: this.filteredData.map((item, index) => [
          index + 1,
          item.sesi,
          item.nama,
          item.detail_ranah,
          item.jam_hadir,
        ]),
        startY: 5.5,
        margin: { top: 1, right: 1, left: 1, bottom: 2 },
        styles: {
          fontSize: 10,
          cellPadding: 0.5,
        },
        pageBreak: "auto",
        bodyStyles: {
          minCellHeight: 0.5,
        },
        columnStyles: {
          0: { cellWidth: 2 },
          3: { cellWidth: 5 },
        },
        didDrawPage: (data) => {
          const pageNum = data.pageNumber;
          const pageHeight = doc.internal.pageSize.height;
          const pageWidth = doc.internal.pageSize.width;

          doc.setFontSize(10);

          const footerY = pageHeight - 1.5;

          doc.setDrawColor(200, 200, 200);
          doc.setLineWidth(0.02);
          doc.line(1, footerY, pageWidth - 1, footerY);

          const footerText = `ABSENSI ${this.filters.acara} - ${this.filters.ranah} - ${this.filters.tanggal} - Halaman ${pageNum}`;
          doc.text(footerText, pageWidth - 1, footerY + 0.5, {
            align: "right",
          });
        },
      });

      const filename = `kehadiran-acara-${this.filters.acara || "UMUM"}-${this.filters.sesi || "semua"}-${this.filters.ranah || "semua"}-${this.filters.nama || "semua"}-${this.filters.tanggal || "semua"}.pdf`;
      doc.save(filename);
    },
    async downloadAllPDF() {
      const doc = new jsPDF({
        unit: "cm",
        format: "a4",
        margins: { top: 1, bottom: 1, left: 1, right: 1 },
      });

      const ranahList = [
        ...new Set(this.filteredData.map((item) => item.ranah)),
      ];

      const recursiveProcess = (index = 0) => {
        if (index >= ranahList.length) {
          doc.save("kehadiran-all.pdf");
          return;
        }

        const currentRanah = ranahList[index];

        if (index > 0) {
          doc.addPage();
        }

        doc.setFont("Times", "normal");
        doc.setFontSize(20);
        doc.text(
          "Laporan Kehadiran Acara",
          doc.internal.pageSize.getWidth() / 2,
          2,
          { align: "center" },
        );

        doc.setFontSize(16);
        doc.text(
          `${this.filters.acara || "UMUM"}`,
          doc.internal.pageSize.getWidth() / 2,
          2.8,
          { align: "center" },
        );

        doc.setFontSize(20);
        doc.text(`${currentRanah}`, doc.internal.pageSize.getWidth() / 2, 3.6, {
          align: "center",
        });

        doc.setFontSize(16);
        const formattedTanggal = this.formatTanggal(this.filters.tanggal);
        doc.text(
          `${formattedTanggal}`,
          doc.internal.pageSize.getWidth() / 2,
          4.4,
          { align: "center" },
        );

        const dataForRanah = this.filteredData.filter(
          (item) => item.ranah === currentRanah,
        );

        autoTable(doc, {
          head: [["No.", "Sesi", "Nama", "Kelompok", "Jam Hadir"]],
          body: dataForRanah.map((item, idx) => [
            idx + 1,
            item.sesi,
            item.nama,
            item.detail_ranah,
            item.jam_hadir,
          ]),
          startY: 5.5,
          margin: { top: 1, right: 1, left: 1, bottom: 2 },
          styles: {
            fontSize: 10,
            cellPadding: 0.5,
          },
          pageBreak: "auto",
          bodyStyles: { minCellHeight: 0.5 },
          columnStyles: {
            0: { cellWidth: 2 },
            3: { cellWidth: 5 },
          },
          didDrawPage: (_data) => {
            const pageInfo = doc.internal.getCurrentPageInfo();
            const pageNum = pageInfo.pageNumber;
            const pageHeight = doc.internal.pageSize.height;
            const pageWidth = doc.internal.pageSize.width;

            doc.setFontSize(10);
            const footerY = pageHeight - 1.5;

            doc.setDrawColor(200, 200, 200);
            doc.setLineWidth(0.02);
            doc.line(1, footerY, pageWidth - 1, footerY);

            const footerText = `ABSENSI ${this.filters.acara} - ${currentRanah} - ${this.filters.tanggal} - Halaman ${pageNum}`;
            doc.text(footerText, pageWidth - 1, footerY + 0.5, {
              align: "right",
            });
          },
        });

        recursiveProcess(index + 1);
      };

      recursiveProcess();
    },
    sortTable(key) {
      if (this.sortKey === key) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortKey = key;
        this.sortOrder = "asc";
      }
    },
    openStatistics() {
      const url = `stat-ngaji.html?key=${this.apiKey}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi}`;
      window.open(url, "_blank");
    },
    filterTable() {
      // This method doesn't exist in the original code but is called in the template
      // It's empty since filtering is handled by the computed property
    },
  },
  watch: {
    "filters.acara"(newValue) {
      document.title = `Pantauan Kehadiran Acara - ${newValue || "UMUM"}`;
    },
    "filters.ranah"() {
      // Reset kelompok filter when desa filter changes
      this.filters.kelompok = "";
    },
  },
  mounted() {
    const today = new Date();
    this.filters.tanggal = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
    const params = new URLSearchParams(window.location.search);
    this.apiKey = params.get("key");
    this.filters.acara = params.get("acara") || "";
    this.filters.lokasi = params.get("lokasi") || "";
    this.fetchDataForDate();
    document.title = `Pantauan Kehadiran - ${this.filters.acara || "UMUM"}`;
  },
};
</script>

<style>
body {
  margin: 20px;
  background-color: #f0f2f5;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

h1 {
  font-size: 2.5em;
  text-align: center;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #ffffff;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  margin-bottom: 20px;
  max-width: 550px; /* Changed from 400px to match table container */
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.filter-item div {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: 5px;
  font-weight: bold;
}

select,
input,
button {
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  appearance: none;
  border-radius: 20px;
  box-sizing: border-box;
  width: 100%;
  height: 45px;
  /* Improve touch targets for mobile */
  min-height: 44px;
  touch-action: manipulation;
}

select,
input {
  margin-top: 0;
  margin-bottom: 0;
}

button {
  width: auto;
  margin: 20px 10px;
  padding: 0 20px;
  display: inline-block;
  cursor: pointer;
  height: 50px;
}

.table-container {
  width: 100%;
  max-width: 768px;
  overflow-x: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  margin: 0 auto;
  /* Improve mobile scrolling */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  min-width: 100%;
}

th,
td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  white-space: nowrap;
}

th {
  background-color: #f4f4f4;
  cursor: pointer;
  position: relative;
  user-select: none;
  transition: background-color 0.2s;
}

th:hover {
  background-color: #e0e0e0;
}

th span {
  margin-left: 5px;
  color: #666;
}

th:first-child,
th:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 20;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

td:first-child,
td:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 10;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

tr:last-child td {
  border-bottom: none;
}

th:first-child {
  cursor: default;
}

th:first-child:hover {
  background-color: #f4f4f4;
}

.button-container {
  text-align: center;
  margin-top: 20px;
  width: 100%;
  max-width: 550px;
  margin-left: auto;
  margin-right: auto;
}

/* Tablet and small desktop */
@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    max-width: 100%;
  }

  button {
    width: 100%;
  }
}

/* Mobile devices - General improvements */
@media (max-width: 480px) {
  body {
    margin: 10px;
  }

  h1 {
    font-size: 2em;
    margin-bottom: 15px;
  }

  .filter-item {
    padding: 15px;
    gap: 12px;
    margin-bottom: 15px;
  }

  .table-container {
    font-size: 14px;
  }

  th,
  td {
    padding: 8px 5px;
    font-size: 13px;
  }

  th:first-child,
  td:first-child {
    width: 40px;
    min-width: 40px;
  }

  button {
    height: 48px;
    font-size: 16px;
    margin: 10px 5px;
  }

  .button-container {
    margin-top: 15px;
  }

  /* Stack buttons vertically on mobile for better fit */
  .button-container button {
    display: block;
    width: 100%;
    margin: 8px 0;
  }
}

/* iPhone 11/XR and similar devices */
@media (max-width: 414px) {
  h1 {
    font-size: 1.8em;
  }

  .filter-item {
    padding: 12px;
    border-radius: 15px;
  }

  select,
  input {
    height: 48px;
    font-size: 16px;
    padding: 12px;
  }

  .table-container {
    font-size: 13px;
    border-radius: 8px;
  }

  th,
  td {
    padding: 6px 3px;
    font-size: 12px;
  }

  /* Optimize column widths for iPhone 11/XR with Sesi column */
  th:first-child,
  td:first-child {
    width: 35px;
    min-width: 35px;
  }

  th:nth-child(2),
  td:nth-child(2) {
    min-width: 60px; /* Sesi column */
  }

  th:nth-child(3),
  td:nth-child(3) {
    min-width: 110px; /* Nama column */
  }

  th:nth-child(4),
  td:nth-child(4) {
    min-width: 70px; /* Desa column */
  }

  th:nth-child(5),
  td:nth-child(5) {
    min-width: 90px; /* Kelompok column */
  }

  th:nth-child(6),
  td:nth-child(6) {
    min-width: 80px; /* Jam Hadir column */
  }

  button {
    height: 50px;
    margin: 8px 0;
    border-radius: 15px;
  }
}

/* iPhone SE and smaller devices */
@media (max-width: 375px) {
  h1 {
    font-size: 1.6em;
    margin-bottom: 10px;
  }

  .filter-item {
    padding: 10px;
    gap: 10px;
  }

  .filter-item label {
    font-size: 14px;
  }

  select,
  input {
    height: 46px;
    font-size: 15px;
    padding: 10px;
  }

  .table-container {
    font-size: 12px;
  }

  th,
  td {
    padding: 5px 2px;
    font-size: 11px;
  }

  /* Further optimize for very small screens with Sesi column */
  th:first-child,
  td:first-child {
    width: 30px;
    min-width: 30px;
  }

  th:nth-child(2),
  td:nth-child(2) {
    min-width: 50px; /* Sesi column */
  }

  th:nth-child(3),
  td:nth-child(3) {
    min-width: 95px; /* Nama column */
  }

  th:nth-child(4),
  td:nth-child(4) {
    min-width: 60px; /* Desa column */
  }

  th:nth-child(5),
  td:nth-child(5) {
    min-width: 75px; /* Kelompok column */
  }

  th:nth-child(6),
  td:nth-child(6) {
    min-width: 70px; /* Jam Hadir column */
  }

  button {
    height: 48px;
    font-size: 15px;
    margin: 6px 0;
  }

  .button-container {
    padding: 0 5px;
  }
}

section {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
