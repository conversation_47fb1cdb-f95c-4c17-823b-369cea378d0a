<template>
  <div id="app">
    <!-- <h1>Pantauan Kehadiran Acara</h1> -->
    <h1>Pantauan</h1>
    <div style="margin-bottom: 15px; font-weight: bold; text-align: center">
      {{ filters.acara || "UMUM" }}
    </div>

    <section>
      <div class="filter-item">
        <div>
          <label for="desaFilter">Filter Desa:</label>
          <select
            v-model="filters.ranah"
            @change="onDesaChange"
            id="desaFilter"
          >
            <option value="">Semua</option>
            <option v-for="ranah in uniqueRanah" :key="ranah" :value="ranah">
              {{ ranah }}
            </option>
          </select>
        </div>
        <div>
          <label for="kelompokFilter">Filter Kelompok:</label>
          <select
            v-model="filters.kelompok"
            @change="filterTable"
            id="kelompokFilter"
          >
            <option value="">Semua</option>
            <option
              v-for="kelompok in availableKelompok"
              :key="kelompok"
              :value="kelompok"
            >
              {{ kelompok }}
            </option>
          </select>
        </div>
        <div>
          <label for="tanggalFilter">Filter Tanggal:</label>
          <input
            type="date"
            v-model="filters.tanggal"
            @input="fetchDataForDate"
            id="tanggalFilter"
          />
        </div>
        <div>
          <label for="namaFilter">Filter Nama:</label>
          <input
            type="text"
            v-model="filters.nama"
            @input="filterTable"
            placeholder="Cari nama..."
            id="namaFilter"
          />
        </div>
      </div>
    </section>

    <div class="table-container">
      <table id="attendanceTable">
        <thead>
          <tr>
            <th>No.</th>
            <th @click="sortTable('nama')">
              Nama
              <span v-if="sortKey === 'nama'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('ranah')">
              Desa
              <span v-if="sortKey === 'ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('detail_ranah')">
              Kelompok
              <span v-if="sortKey === 'detail_ranah'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th @click="sortTable('jam_hadir')">
              Jam Hadir
              <span v-if="sortKey === 'jam_hadir'">{{
                sortOrder === "asc" ? "↑" : "↓"
              }}</span>
            </th>
            <th style="display: none">Tanggal</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in filteredData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.nama }}</td>
            <td>{{ item.ranah }}</td>
            <td>{{ item.detail_ranah }}</td>
            <td>{{ item.jam_hadir }}</td>
            <td style="display: none">{{ item.tanggal }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="button-container">
      <button @click="downloadPDF">Download as PDF</button>
      <button @click="openStatistics">View Statistics</button>
    </div>
  </div>
</template>

<script>
import { jsPDF } from "jspdf";
import "jspdf-autotable";

export default {
  data() {
    return {
      attendanceData: [],
      filters: {
        ranah: "",
        kelompok: "",
        nama: "",
        tanggal: "",
        acara: "",
        lokasi: "",
      },
      apiKey: "",
      sortKey: "",
      sortOrder: "asc",
    };
  },
  computed: {
    uniqueSesi() {
      return [...new Set(this.attendanceData.map((item) => item.sesi))].sort();
    },
    uniqueRanah() {
      return [...new Set(this.attendanceData.map((item) => item.ranah))].sort();
    },
    availableKelompok() {
      if (!this.filters.ranah) {
        // If no desa is selected, show all kelompok
        return [
          ...new Set(this.attendanceData.map((item) => item.detail_ranah)),
        ].sort();
      } else {
        // If desa is selected, show only kelompok from that desa
        return [
          ...new Set(
            this.attendanceData
              .filter((item) => item.ranah === this.filters.ranah)
              .map((item) => item.detail_ranah),
          ),
        ].sort();
      }
    },
    filteredData() {
      const filtered = this.attendanceData.filter((item) => {
        const sesiMatch = !this.filters.sesi || item.sesi === this.filters.sesi;
        const ranahMatch =
          !this.filters.ranah || item.ranah === this.filters.ranah;
        const kelompokMatch =
          !this.filters.kelompok || item.detail_ranah === this.filters.kelompok;
        const namaMatch =
          !this.filters.nama ||
          item.nama.toLowerCase().includes(this.filters.nama.toLowerCase());
        return sesiMatch && ranahMatch && kelompokMatch && namaMatch;
      });

      if (this.sortKey) {
        filtered.sort((a, b) => {
          let aVal = this.sortKey === "index" ? 1 : a[this.sortKey];
          let bVal = this.sortKey === "index" ? 1 : b[this.sortKey];

          if (typeof aVal === "string") aVal = aVal.toLowerCase();
          if (typeof bVal === "string") bVal = bVal.toLowerCase();

          if (aVal < bVal) return this.sortOrder === "asc" ? -1 : 1;
          if (aVal > bVal) return this.sortOrder === "asc" ? 1 : -1;
          return 0;
        });
      }

      return filtered;
    },
  },
  methods: {
    filterTable() {
      // Empty method to handle @change event on filters
    },
    onDesaChange() {
      // Reset kelompok filter when desa changes
      this.filters.kelompok = "";
      this.filterTable();
    },
    formatDateIndonesian(dateStr) {
      const date = new Date(dateStr);
      const days = [
        "Minggu",
        "Senin",
        "Selasa",
        "Rabu",
        "Kamis",
        "Jumat",
        "Sabtu",
      ];
      const months = [
        "Januari",
        "Februari",
        "Maret",
        "April",
        "Mei",
        "Juni",
        "Juli",
        "Agustus",
        "September",
        "Oktober",
        "November",
        "Desember",
      ];
      return `${days[date.getDay()]}, ${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
    },
    async fetchDataForDate() {
      const apiUrl = `/api/absen-pengajian/?tanggal=${this.filters.tanggal}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi || ""}`;
      try {
        const response = await fetch(apiUrl, {
          headers: { Authorization: `ApiKey ${this.apiKey}` },
        });
        if (!response.ok)
          throw new Error(`HTTP error! status: ${response.status}`);
        this.attendanceData = await response.json();
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    },
    downloadPDF() {
      const doc = new jsPDF({
        unit: "cm",
        format: "a4",
        margins: { top: 1, bottom: 1, left: 1, right: 1 },
      });

      doc.setFont("times", "normal");
      doc.setFontSize(20);
      doc.text(
        "Laporan Kehadiran Acara",
        doc.internal.pageSize.getWidth() / 2,
        2,
        { align: "center" },
      );
      doc.setFontSize(16);
      doc.text(
        `${this.filters.acara} - ${this.filters.ranah || "Semua"}`,
        doc.internal.pageSize.getWidth() / 2,
        2.8,
        { align: "center" },
      );
      doc.text(
        `${this.formatDateIndonesian(this.filters.tanggal)}`,
        doc.internal.pageSize.getWidth() / 2,
        3.6,
        { align: "center" },
      );

      doc.autoTable({
        head: [["No.", "Nama", "Kelompok", "Jam Hadir"]],
        body: this.filteredData.map((item, index) => [
          index + 1,
          item.nama,
          item.detail_ranah,
          item.jam_hadir,
        ]),
        startY: 4.5,
        margin: { top: 1, right: 1, left: 1, bottom: 2 }, // Reserve space for footer
        styles: {
          fontSize: 10,
          cellPadding: 0.5,
        },
        pageBreak: "auto",
        bodyStyles: {
          minCellHeight: 0.5,
        },
        columnStyles: {
          0: { cellWidth: 2 }, // No. column
          3: { cellWidth: 3 }, // Jam Hadir column
        },
        didDrawPage: (data) => {
          // Add footer to every page
          const pageNum = data.pageNumber;
          const pageHeight = doc.internal.pageSize.height;
          const pageWidth = doc.internal.pageSize.width;

          // Configure footer style
          doc.setFontSize(10);

          // Fixed footer position, 1.5cm from bottom
          const footerY = pageHeight - 1.5;

          // Draw separator line
          doc.setDrawColor(200, 200, 200);
          doc.setLineWidth(0.02);
          doc.line(1, footerY, pageWidth - 1, footerY);

          // Add footer text
          const formattedDate = this.formatDateIndonesian(this.filters.tanggal);
          const footerText = `ABSENSI ${this.filters.acara} - ${this.filters.ranah} - ${formattedDate} - Halaman ${pageNum}`;
          doc.text(footerText, pageWidth - 1, footerY + 0.5, {
            align: "right",
          });
        },
      });

      const filename = `Laporan-Absen-${this.filters.ranah || "semua"}-${this.filters.acara || "UMUM"}-${this.filters.tanggal || "semua"}.pdf`;
      doc.save(filename);
    },
    sortTable(key) {
      if (this.sortKey === key) {
        this.sortOrder = this.sortOrder === "asc" ? "desc" : "asc";
      } else {
        this.sortKey = key;
        this.sortOrder = "asc";
      }
    },
    openStatistics() {
      const url = `stat-ngaji.html?key=${this.apiKey}&acara=${this.filters.acara}&lokasi=${this.filters.lokasi}`;
      window.open(url, "_blank");
    },
  },
  watch: {
    "filters.acara"(newValue) {
      document.title = `Pantauan Kehadiran Acara - ${newValue || "UMUM"}`;
    },
  },
  mounted() {
    const params = new URLSearchParams(window.location.search);

    // Set default date to today
    const today = new Date();
    const defaultDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;

    // Read URL parameters
    this.apiKey = params.get("key");
    this.filters.acara = params.get("acara") || "";
    this.filters.lokasi = params.get("lokasi") || "";
    this.filters.tanggal = params.get("tanggal") || defaultDate;

    this.fetchDataForDate();
    document.title = `Pantauan Kehadiran Acara - ${this.filters.acara || "UMUM"}`;
  },
};
</script>

<style>
body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu,
    Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

h1 {
  font-size: 1.75em;
  text-align: center;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #ffffff;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  margin-bottom: 20px;
  max-width: 550px; /* Changed from 400px to match table container */
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.filter-item div {
  display: flex;
  flex-direction: column;
}

.filter-item label {
  margin-bottom: 5px;
  font-weight: bold;
}

select,
input,
button {
  border: 1px solid #ccc;
  padding: 10px;
  font-size: 16px;
  background-color: #f9f9f9;
  appearance: none;
  border-radius: 20px;
  box-sizing: border-box;
  width: 100%;
  height: 45px;
  /* Improve touch targets for mobile */
  min-height: 44px;
  touch-action: manipulation;
}

select,
input {
  margin-top: 0;
  margin-bottom: 0;
}

button {
  width: auto;
  margin: 20px 10px;
  padding: 0 20px;
  display: inline-block;
  cursor: pointer;
  height: 50px;
}

.table-container {
  width: 100%;
  max-width: 768px;
  overflow-x: auto;
  border: 1px solid #ddd;
  border-radius: 10px;
  margin: 0 auto;
  /* Improve mobile scrolling */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
}

table {
  width: 100%;
  border-collapse: collapse;
  table-layout: auto;
  min-width: 100%;
}

th,
td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  white-space: nowrap;
}

th {
  background-color: #f4f4f4;
  cursor: pointer;
  position: relative;
  user-select: none;
  transition: background-color 0.2s;
}

th:hover {
  background-color: #e0e0e0;
}

th span {
  margin-left: 5px;
  color: #666;
}

th:first-child,
th:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 20;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

td:first-child,
td:nth-child(2) {
  position: sticky;
  left: 0;
  background-color: #fff;
  z-index: 10;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

tr:last-child td {
  border-bottom: none;
}

/* Remove cursor pointer from first column */
th:first-child {
  cursor: default;
}

th:first-child:hover {
  background-color: #f4f4f4; /* Remove hover effect for first column */
}

/* Add a container for buttons to center them */
.button-container {
  text-align: center;
  margin-top: 20px;
  width: 100%;
  max-width: 550px;
  margin-left: auto;
  margin-right: auto;
}

/* Tablet and small desktop */
@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
    max-width: 100%;
  }

  button {
    width: 100%;
  }
}

/* Mobile devices - General improvements */
@media (max-width: 480px) {
  body {
    margin: 10px;
  }

  h1 {
    font-size: 2em;
    margin-bottom: 15px;
  }

  .filter-item {
    padding: 15px;
    gap: 12px;
    margin-bottom: 15px;
  }

  .table-container {
    font-size: 14px;
  }

  th,
  td {
    padding: 8px 6px;
    font-size: 13px;
  }

  th:first-child,
  td:first-child {
    width: 40px;
    min-width: 40px;
  }

  button {
    height: 48px;
    font-size: 16px;
    margin: 10px 5px;
  }

  .button-container {
    margin-top: 15px;
  }
}

/* iPhone 11/XR and similar devices */
@media (max-width: 414px) {
  h1 {
    font-size: 1.8em;
  }

  .filter-item {
    padding: 12px;
    border-radius: 15px;
  }

  select,
  input {
    height: 48px;
    font-size: 16px;
    padding: 12px;
  }

  .table-container {
    font-size: 13px;
    border-radius: 8px;
  }

  th,
  td {
    padding: 6px 4px;
    font-size: 12px;
  }

  /* Optimize column widths for iPhone 11/XR */
  th:first-child,
  td:first-child {
    width: 35px;
    min-width: 35px;
  }

  th:nth-child(2),
  td:nth-child(2) {
    min-width: 120px;
  }

  th:nth-child(3),
  td:nth-child(3) {
    min-width: 80px;
  }

  th:nth-child(4),
  td:nth-child(4) {
    min-width: 100px;
  }

  th:nth-child(5),
  td:nth-child(5) {
    min-width: 90px;
  }

  button {
    height: 50px;
    margin: 8px 3px;
    border-radius: 15px;
  }
}

/* iPhone SE and smaller devices */
@media (max-width: 375px) {
  h1 {
    font-size: 1.6em;
    margin-bottom: 10px;
  }

  .filter-item {
    padding: 10px;
    gap: 10px;
  }

  .filter-item label {
    font-size: 14px;
  }

  select,
  input {
    height: 46px;
    font-size: 15px;
    padding: 10px;
  }

  .table-container {
    font-size: 12px;
  }

  th,
  td {
    padding: 5px 3px;
    font-size: 11px;
  }

  /* Further optimize for very small screens */
  th:first-child,
  td:first-child {
    width: 30px;
    min-width: 30px;
  }

  th:nth-child(2),
  td:nth-child(2) {
    min-width: 100px;
  }

  th:nth-child(3),
  td:nth-child(3) {
    min-width: 70px;
  }

  th:nth-child(4),
  td:nth-child(4) {
    min-width: 85px;
  }

  th:nth-child(5),
  td:nth-child(5) {
    min-width: 80px;
  }

  button {
    height: 48px;
    font-size: 15px;
    margin: 6px 2px;
  }

  .button-container {
    padding: 0 5px;
  }
}

section {
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>
