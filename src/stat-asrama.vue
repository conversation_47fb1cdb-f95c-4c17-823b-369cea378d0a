<template>
    <div id="pieChartSection">
        <h2>Statistik Kehadiran Asrama</h2>
        <div class="mb-15">
            {{ acara || "ASRAMA" }}
        </div>

        <div class="mb-10">
            <label for="tanggalFilter">Tanggal:</label>
            <input
                type="date"
                ref="dateInput"
                v-model="selectedDate"
                @change="fetchData"
            />
        </div>
        <div class="mb-10">
            <label for="sesiFilter">Sesi:</label>
            <select v-model="selectedSesi" @change="fetchData">
                <option value="">Semua Sesi</option>
                <option v-for="sesi in sesiOptions" :key="sesi" :value="sesi">
                    {{ sesi }}
                </option>
            </select>
        </div>
        <canvas ref="timePieChart" width="150" height="150"></canvas>

        <table id="statisticsTable">
            <thead>
                <tr>
                    <th>Kehadiran</th>
                    <th>Jumlah</th>
                    <th>Persentase</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="stat in statistics" :key="stat.category">
                    <td>{{ stat.category }}</td>
                    <td>{{ stat.count }}</td>
                    <td>{{ stat.percentage }}%</td>
                </tr>
                <tr>
                    <td><strong>Total</strong></td>
                    <td>
                        <strong>{{ totalCount }}</strong>
                    </td>
                    <td><strong>100%</strong></td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
// Initialize Chart.js once
let Chart = null;
let chartInstance = null;

const initChart = async () => {
    if (!Chart) {
        await new Promise((resolve) => {
            const script = document.createElement("script");
            script.src = "https://cdn.jsdelivr.net/npm/chart.js@3.8.0";
            script.onload = () => {
                Chart = window.Chart;
                Chart.register(
                    Chart.Title,
                    Chart.Tooltip,
                    Chart.Legend,
                    Chart.ArcElement,
                    Chart.CategoryScale,
                    Chart.LinearScale,
                );
                resolve();
            };
            document.head.appendChild(script);
        });
    }
    return Chart;
};

export default {
    name: "StatAsrama",
    data() {
        return {
            selectedDate: "", // Set in mounted
            selectedSesi: "",
            sesiOptions: [],
            referenceTime: "",
            statistics: [],
            chart: null,
            fetchedData: [],
            acara: "",
            isLoading: false,
            sesiData: [], // Store full sesi data
        };
    },
    async created() {
        await this.fetchSesiData();
    },
    computed: {
        totalCount() {
            return this.statistics.reduce(
                (sum, stat) => sum + Number(stat.count),
                0,
            );
        },
    },
    watch: {
        referenceTime: {
            handler(_newTime) {
                if (this.fetchedData?.length) {
                    this.onReferenceTimeChange();
                }
            },
            immediate: false,
        },
        acara: {
            handler(newAcara) {
                document.title = newAcara
                    ? `Statistik Kehadiran Asrama - ${newAcara}`
                    : "Statistik Kehadiran Asrama";
            },
            immediate: true,
        },
        selectedDate: {
            handler(newDate) {
                if (!newDate) return;

                console.log("Date changed to:", newDate);
                if (chartInstance) {
                    chartInstance.destroy();
                    chartInstance = null;
                }

                this.fetchedData = [];
                this.statistics = [];
                this.fetchData();
            },
            immediate: false,
        },
        selectedSesi: {
            async handler(newSesi) {
                if (chartInstance) {
                    chartInstance.destroy();
                    chartInstance = null;
                }

                // Update reference time based on selected sesi
                if (newSesi && this.sesiData) {
                    const sesiInfo = this.sesiData.find(
                        (item) => item.sesi === newSesi,
                    );
                    if (sesiInfo?.waktu) {
                        this.referenceTime = sesiInfo.waktu;
                    }
                }

                this.fetchedData = [];
                this.statistics = [];
                this.fetchData();
            },
        },
    },
    methods: {
        async fetchSesiData() {
            try {
                const params = new URLSearchParams(window.location.search);
                const sesiParam = params.get("sesi");
                if (!sesiParam) {
                    console.warn("Parameter sesi tidak ditemukan di URL");
                    return;
                }

                const encodedParam = encodeURIComponent(sesiParam);
                const response = await fetch(
                    `/api/data/sesi/${encodedParam}`,
                );

                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const data = await response.json();
                if (Array.isArray(data)) {
                    this.sesiData = data;
                    this.sesiOptions = data.map((item) => item.sesi);

                    // Set initial reference time
                    const urlTime = new URLSearchParams(
                        window.location.search,
                    ).get("time");
                    if (urlTime) {
                        this.referenceTime = urlTime;
                    } else if (this.selectedSesi) {
                        const sesiInfo = data.find(
                            (item) => item.sesi === this.selectedSesi,
                        );
                        if (sesiInfo?.waktu) {
                            this.referenceTime = sesiInfo.waktu;
                        }
                    }

                    if (!this.referenceTime) {
                        this.referenceTime = "08:55"; // fallback default
                    }
                }
            } catch (error) {
                console.error("Error fetching sesi data:", error);
                console.warn("Failed to fetch sesi data:", error.message);
                this.referenceTime = "08:55"; // Set default on error
            }
        },

        async fetchData() {
            if (this.isLoading) {
                console.log("Request in progress, skipping new request");
                return;
            }

            try {
                this.isLoading = true;
                console.log("Fetching data for date:", this.selectedDate);

                const params = new URLSearchParams(window.location.search);
                const apiKey = params.get("key");
                this.acara = params.get("acara") || "";
                const lokasi = params.get("lokasi") || "";

                let apiUrl = `/api/absen-asramaan/?tanggal=${this.selectedDate}&acara=${encodeURIComponent(this.acara)}&lokasi=${encodeURIComponent(lokasi)}`;
                if (this.selectedSesi) {
                    apiUrl += `&sesi=${encodeURIComponent(this.selectedSesi)}`;
                }

                console.log("Requesting URL:", apiUrl);
                const response = await fetch(apiUrl, {
                    headers: {
                        Authorization: `ApiKey ${apiKey}`,
                        Accept: "application/json",
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                if (!Array.isArray(data)) {
                    console.error("Invalid data format received:", data);
                    this.handleNoData();
                    return;
                }

                this.fetchedData = data;
                this.processData(this.fetchedData);
            } catch (error) {
                console.error("Error fetching data:", error);
                this.handleNoData();
                console.warn(
                    `Failed to fetch attendance data: ${error.message}`,
                );
            } finally {
                this.isLoading = false;
            }
        },

        categorizeAttendance(jam) {
            if (!jam) return "Unknown";

            const [jamHour, jamMinutes] = jam.split(":").map(Number);
            if (Number.isNaN(jamHour) || Number.isNaN(jamMinutes))
                return "Unknown";
            const jamTime = jamHour * 60 + jamMinutes;

            const [refHour, refMinutes] = this.referenceTime
                .split(":")
                .map(Number);
            const refTime = refHour * 60 + refMinutes;

            if (jamTime < refTime) return "In Time";
            if (jamTime <= refTime + 15) return "On Time";
            return "Terlambat";
        },

        initializeChart(canvas) {
            canvas.classList.add("chart-canvas");
            canvas.parentElement?.classList.add("chart-container");
        },

        async updateChart(aggregation) {
            try {
                const canvas = this.$refs.timePieChart;
                if (!canvas) {
                    console.error("Canvas element not found");
                    return;
                }

                if (!canvas.classList.contains("chart-canvas")) {
                    this.initializeChart(canvas);
                }

                const Chart = await initChart();

                const labels = Object.keys(aggregation);
                const data = Object.values(aggregation);
                const chartColors = {
                    "In Time": "#82EE84",
                    "On Time": "#36A2EB",
                    Terlambat: "#FF2323",
                    Unknown: "#CCCCCC",
                };
                const backgroundColors = labels.map(
                    (label) => chartColors[label] || "#CCCCCC",
                );

                if (chartInstance) {
                    chartInstance.data.labels = labels;
                    chartInstance.data.datasets[0].data = data;
                    chartInstance.update("none");
                    return;
                }

                const config = {
                    type: "pie",
                    data: {
                        labels,
                        datasets: [
                            {
                                data,
                                backgroundColor: backgroundColors,
                            },
                        ],
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        animation: {
                            duration: 300,
                        },
                        plugins: {
                            legend: {
                                position: "bottom",
                                labels: {
                                    boxWidth: 20,
                                    padding: 10,
                                },
                            },
                            title: {
                                display: true,
                                text: "Distribusi Kehadiran Berdasarkan Waktu",
                                padding: 10,
                            },
                        },
                    },
                };

                chartInstance = new Chart(canvas, config);
            } catch (error) {
                console.error("Error updating chart:", error);
                this.handleChartError(error);
            }
        },

        handleChartError(error) {
            if (chartInstance) {
                chartInstance.destroy();
                chartInstance = null;
            }
            console.error("Chart error:", error.message);
        },

        handleNoData() {
            if (chartInstance) {
                chartInstance.destroy();
                chartInstance = null;
            }
            this.statistics = [];
            this.fetchedData = [];
            console.warn("No attendance data available for the selected date.");
        },

        processData(data) {
            if (!Array.isArray(data) || !data.length) {
                console.error("Invalid or empty data format:", data);
                this.handleNoData();
                return;
            }

            try {
                const aggregation = data.reduce((acc, item) => {
                    if (!item?.jam_hadir) return acc;
                    const category = this.categorizeAttendance(item.jam_hadir);
                    if (category) {
                        acc[category] = (acc[category] || 0) + 1;
                    }
                    return acc;
                }, {});

                if (Object.keys(aggregation).length === 0) {
                    this.handleNoData();
                    return;
                }

                requestAnimationFrame(() => {
                    this.updateChart(aggregation);
                    this.updateStatistics(aggregation);
                });
            } catch (error) {
                console.error("Error processing data:", error);
                this.handleNoData();
            }
        },

        updateStatistics(aggregation) {
            const total = Object.values(aggregation).reduce(
                (sum, count) => sum + Number(count),
                0,
            );
            this.statistics = Object.entries(aggregation).map(
                ([category, count]) => ({
                    category,
                    count,
                    percentage: ((count / total) * 100).toFixed(1),
                }),
            );
        },

        onReferenceTimeChange() {
            console.log("Reference time changed to:", this.referenceTime);
            if (this.fetchedData?.length) {
                // Force chart to completely redraw for more reliable update
                if (chartInstance) {
                    chartInstance.destroy();
                    chartInstance = null;
                }
                this.processData(this.fetchedData);
            }
        },
    },
    async mounted() {
        try {
            const today = new Date();
            this.selectedDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, "0")}-${String(today.getDate()).padStart(2, "0")}`;
            await this.$nextTick();
            const dateInput = this.$refs.dateInput;
            if (dateInput) {
                dateInput.addEventListener("change", (e) => {
                    this.selectedDate = e.target.value;
                });
            }
            await this.fetchData();
        } catch (error) {
            console.error("Error in mounted hook:", error);
            this.handleNoData();
        }
    },
};
</script>

<style>
.chart-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    aspect-ratio: 3/2;
}
.chart-canvas {
    width: 100%;
    height: 100%;
}

#pieChartSection {
    margin-top: 20px;
    text-align: center;
}

#tanggalFilter {
    padding: 5px;
    margin-left: 10px;
}

select {
    padding: 5px;
    margin-left: 10px;
}

#statisticsTable {
    margin: 20px auto;
    border-collapse: collapse;
    width: 50%;
}

#statisticsTable th,
#statisticsTable td {
    border: 1px solid #ddd;
    padding: 8px;
}

#statisticsTable th {
    background-color: #f2f2f2;
    font-weight: bold;
}

#statisticsTable td {
    text-align: center;
}

.mb-15 {
    margin-bottom: 15px;
    font-weight: bold;
    text-align: center;
}

.mb-10 {
    margin-bottom: 10px;
}
</style>
